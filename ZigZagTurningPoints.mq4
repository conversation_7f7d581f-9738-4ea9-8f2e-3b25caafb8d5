//+------------------------------------------------------------------+
//|                                          ZigZagTurningPoints.mq4 |
//|                                          Copyright 2023           |
//|                                                                   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023"
#property link      ""
#property version   "1.00"
#property strict
#property indicator_chart_window

// 输入参数
input int    ZigZag_Depth=12;     // ZigZag深度
input int    ZigZag_Deviation=5;   // ZigZag偏差
input int    ZigZag_Backstep=3;    // ZigZag回溯
input color  PointColor=clrRed;    // 转折点颜色
input int    PointSize=10;         // 点的大小
input int    StartBar=4;           // 从第几根K线开始查找(默认第4根)
input color  BullishColor=clrLime; // 看涨趋势颜色
input color  BearishColor=clrRed;  // 看跌趋势颜色
input int    FontSize=10;          // 文字大小
input bool   ShowFiboLines=true;   // 显示斐波那契线
input color  FiboLineColor=clrGold;// 斐波那契线颜色
input int    FiboLineStyle=STYLE_DOT; // 斐波那契线样式
input int    FiboLineWidth=1;      // 斐波那契线宽度

// 斐波那契档位设置
input double Fibo_Level0=-0.3;     // 第1档
input double Fibo_Level1=0.0;      // 第2档
input double Fibo_Level2=0.236;      // 第3档
input double Fibo_Level3=0.382;     // 第4档
input double Fibo_Level4=0.5;     // 第5档
input double Fibo_Level5=0.618;     // 第6档
input double Fibo_Level6=0.786;     // 第7档
input double Fibo_Level7=0.876;     // 第8档
input double Fibo_Level8=1.0;      // 第9档
input double Fibo_Level9=1.25;     // 第10档
input double Fibo_Level10=1.5;     // 第11档

// 交易参数
input bool   EnableTrading=false;  // 启用自动交易
input bool   TestMode=false;       // 回测模式(回测时启用可提高速度)
input double InitialLotSize=0.1;   // 初始仓位大小
input double LotMultiplier=1.5;    // 仓位倍数
input int    MagicNumber=12345;    // 魔术编号
input int    Slippage=3;           // 允许滑点(点)
input bool   UseMoneyManagement=false; // 启用资金管理
input double RiskPercent=2.0;      // 风险百分比(启用资金管理时)
input int    TPLevelOffset=3;      // 止盈档位偏移(默认为3，表示最高持仓档位-3)
input int    SLLevelOffset=3;      // 止损档位偏移(默认为3，表示初始档位+3，同时也是最大下单数量)
input bool   EnableAdvancedEntry=false; // 启用提前入场
input double AdvancedEntryDistance=0.03; // 提前入场距离(在价格完全回调/反弹到位之前入场)
input bool   EnableAdvancedTP=false;    // 启用提前止盈
input double AdvancedTPDistance=0.04;   // 提前止盈距离(让止盈更容易达到：看涨时降低止盈价格，看跌时提高止盈价格)
input bool   EnableFixedTP=false;       // 启用固定止盈(后续单使用指定单的止盈价格)
input int    FixedTPOrderNumber=3;      // 固定止盈参考单号(按时间顺序，后续单使用此单的止盈价格)
input bool   EnableMaxOrders=false;    // 启用最多单数限制(独立于止损档位偏移)
input int    MaxOrdersCount=3;          // 最多下单数量(当启用最多单数限制时)

// 新增功能参数
input bool   EnableInitialLevelEntry=false; // 启用触及初始挡位下单功能
input bool   EnableTrailingStop=false;      // 启用移动止损功能

// 初始下单挡位设置
input double D_Value_SuperLow=5.0; // 超低d值下限(5<d<15时交易)
input double D_Value_VeryLow=15.0; // 极低d值下限(15<d<20时交易)
input double D_Value_Low=20.0;     // d值下限(小于此值不交易)
input double D_Value_Mid=30.0;     // d值中值(分隔第五档和第四档)
input double D_Value_High=40.0;    // d值上限(分隔第四档和第三档)
input int    Initial_Level_SuperLow=6; // 超低d值时的初始档位(数字加一，默认七)
input int    Initial_Level_VeryLow=5; // 极低d值时的初始档位(数字加一，默认六)
input int    Initial_Level_Low=4;  // d值>低值时的初始档位(数字加一，默认五)
input int    Initial_Level_Mid=3;  // d值>中值时的初始档位(数字加一，默认四)
input int    Initial_Level_High=2; // d值>高值时的初始档位(数字加一，默认三)


// 斐波那契比例数组(将在OnInit中初始化)
double fiboLevels[11];

// 全局变量
datetime lastTurningPoints[3] = {0, 0, 0};  // 存储最近三个转折点的时间
double lastTurningPrices[3] = {0, 0, 0};    // 存储最近三个转折点的价格
string trendDirection = "";                  // 趋势方向
double d1 = 0, d2 = 0, d = 0;               // 差值计算结果
int dSource = 0;                            // d值来源(1=d1, 2=d2)
double startPrice = 0, endPrice = 0;        // 斐波那契线的起点和终点价格

// 交易相关变量
int initialLevel = 0;                       // 初始下单挡位(根据d值判断)
bool levelOrders[11] = {false, false, false, false, false, false, false, false, false, false, false}; // 记录各档位是否已下单
int ordersPlaced = 0;                       // 当前d值下已下单数量
datetime lastDChange = 0;                   // 最后一次d值变化的时间
double takeProfitPrice = 0;                 // 止盈价格
double stopLossPrice = 0;                   // 止损价格
int maxOrderLevel = 0;                      // 最大持仓档位
bool hasOpenPosition = false;               // 是否有持仓
bool hasClosedPosition = false;             // 是否已有平仓(当前d值)
string positionDirection = "";              // 当前持仓方向("BUY"或"SELL")
double openPositionD = 0;                  // 开仓时记录的d值
bool isFirstPositionPlaced = false;         // 是否已下第一单（用于锁定趋势方向）
double fixedTakeProfitPrice = 0;            // 固定止盈价格（当启用固定止盈功能时使用）
bool isFixedTPSet = false;                  // 是否已设置固定止盈价格

// 新增功能相关变量
bool hasInitialLevelOrder = false;         // 是否已有触及初始挡位订单
int initialLevelOrderTicket = 0;           // 触及初始挡位订单票号
datetime lastTrailingStopCheck = 0;        // 上次移动止损检查时间

// 添加以下全局变量，用于记忆已交易的价格区间
#define MAX_PRICE_MEMORY 20  // 最多记忆20个价格区间
double tradedPriceZones[MAX_PRICE_MEMORY][2]; // [0]=起始价格, [1]=结束价格
int tradedPriceZonesCount = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // 确保EA运行在图表窗口
   ChartSetInteger(0, CHART_FOREGROUND, false);
   
   // 初始化交易区域记录
   ClearTradedPriceZones();
   
   // 初始化斐波那契比例数组
   fiboLevels[0] = Fibo_Level0;
   fiboLevels[1] = Fibo_Level1;
   fiboLevels[2] = Fibo_Level2;
   fiboLevels[3] = Fibo_Level3;
   fiboLevels[4] = Fibo_Level4;
   fiboLevels[5] = Fibo_Level5;
   fiboLevels[6] = Fibo_Level6;
   fiboLevels[7] = Fibo_Level7;
   fiboLevels[8] = Fibo_Level8;
   fiboLevels[9] = Fibo_Level9;
   fiboLevels[10] = Fibo_Level10;
   
   // 验证斐波那契水平是否按升序排列
   for(int i=0; i<10; i++)
   {
      if(fiboLevels[i] >= fiboLevels[i+1])
      {
         Print("错误: 斐波那契水平必须按升序排列! 第", i+1, "档(", fiboLevels[i], ")>=第", i+2, "档(", fiboLevels[i+1], ")");
         return(INIT_PARAMETERS_INCORRECT);
      }
   }
   
   // 验证初始下单档位设置是否有效
   if(Initial_Level_Low < 0 || Initial_Level_Low > 7 ||
      Initial_Level_Mid < 0 || Initial_Level_Mid > 7 ||
      Initial_Level_High < 0 || Initial_Level_High > 7)
   {
      Print("错误: 初始下单档位必须在0-7之间!");
      return(INIT_PARAMETERS_INCORRECT);
   }
   
   // 验证d值阈值设置是否有效
   if(D_Value_Low <= 0 || D_Value_Mid <= D_Value_Low || D_Value_High <= D_Value_Mid)
   {
      Print("错误: d值阈值必须满足: D_Value_Low > 0, D_Value_Mid > D_Value_Low, D_Value_High > D_Value_Mid");
      return(INIT_PARAMETERS_INCORRECT);
   }
   
   // 输出斐波那契档位设置
   if(!TestMode)
   {
      Print("斐波那契档位设置:");
      for(int i=0; i<10; i++)
      {
         Print("第", i+1, "档: ", fiboLevels[i], "-", fiboLevels[i+1]);
      }
   }
   
   // 输出初始下单档位设置
   if(!TestMode)
   {
      Print("初始下单档位设置:");
      Print("d值 > ", D_Value_High, " -> 第", Initial_Level_High+1, "档");
      Print(D_Value_Mid, " < d值 <= ", D_Value_High, " -> 第", Initial_Level_Mid+1, "档");
      Print(D_Value_Low, " < d值 <= ", D_Value_Mid, " -> 第", Initial_Level_Low+1, "档");
      Print("d值 <= ", D_Value_Low, " -> 不交易");
   }
   
   // 初始化时立即查找转折点
   FindZigZagTurningPoints();
   AnalyzeTrend();
   if(!TestMode || IsVisualMode())
      MarkTurningPoints();
   
   // 初始化交易状态
   ResetTradeStatus();
   DetermineInitialLevel();
   
   // 输出EA启动信息
   if(!TestMode)
      Print("ZigZag交易EA已启动，交易功能: ", (EnableTrading ? "已启用" : "未启用"));
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 回测模式非可视化时跳过清除图形对象
   if(!TestMode || IsVisualMode())
   {
      // 清除图表上所有由EA创建的对象
      ObjectsDeleteAll(0, "ZigZagPoint_");
      ObjectsDeleteAll(0, "ZigZagLabel_");
      ObjectsDeleteAll(0, "ZigZagLine_");
      ObjectsDeleteAll(0, "TrendInfo");
      ObjectsDeleteAll(0, "TrendInfo2"); // 添加新标签的删除
      ObjectsDeleteAll(0, "FiboLine_");
      ObjectsDeleteAll(0, "FiboLabel_");
      ObjectsDeleteAll(0, "FiboInfo");
      ObjectsDeleteAll(0, "FiboWaveLine");
      ObjectsDeleteAll(0, "FiboPriceLabel_");
      ObjectsDeleteAll(0, "FiboStartLabel_");
      ObjectsDeleteAll(0, "TradeInfo");
      ObjectsDeleteAll(0, "LevelLabel_");
      ObjectsDeleteAll(0, "InitialOrderLevel");
      ObjectsDeleteAll(0, "InitialLevelLabel");
   }
   
   // 清除交易记录区域
   ClearTradedPriceZones();
   
   // 删除全局变量
   if(GlobalVariableCheck("ZigZag_ResetLastCheckedPrice"))
      GlobalVariableDel("ZigZag_ResetLastCheckedPrice");
      
   // 输出EA停止信息
   if(!TestMode)
      Print("ZigZag交易EA已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // 只在新K线出现时或间隔一定时间进行ZigZag点的更新和趋势分析
   static datetime lastBarTime = 0;
   static datetime lastAnalysisTime = 0;
   static double lastD = 0;
   static double lastStartPrice = 0;
   static double lastEndPrice = 0;
   
   datetime currentBarTime = Time[0];
   datetime currentTime = TimeCurrent();
   
   // 新K线检查
   bool isNewBar = (currentBarTime != lastBarTime);
   // 回测模式下只在新K线时更新，实盘/可视化模式下每10秒更新一次
   bool needUpdate = isNewBar || (!TestMode && currentTime - lastAnalysisTime > 10);
   
   if((isNewBar || needUpdate) && !hasOpenPosition)  // 添加条件：只有在没有持仓时才更新ZigZag转折点
   {
      if(isNewBar)
         lastBarTime = currentBarTime;
      
      lastAnalysisTime = currentTime;
      
      // 保存上次的重要数据
      lastD = d;
      lastStartPrice = startPrice;
      lastEndPrice = endPrice;
      
      // 保存上次d值用于检测变化
      double prevD = d;
      int prevDSource = dSource;
      
      // 查找最近的三个ZigZag转折点
      FindZigZagTurningPoints();
      
      // 分析趋势
      AnalyzeTrend();
      
      // 在图表上标记转折点 - 回测非可视化模式下跳过
      if(!TestMode || IsVisualMode())
         MarkTurningPoints();
      

      
      // 如果没有持仓，或者持仓但使用的是旧d值，才考虑重置交易状态
      if(!hasOpenPosition)
      {
         // 检查d值是否发生明显变化
         if(MathAbs(d - prevD) / prevD > 0.05 || dSource != prevDSource || d == 0)
         {
            // d值变化超过5%或者源变化，重置交易状态
            ResetTradeStatus();
            if(!TestMode)
               Print("d值明显变化: ", prevD, " -> ", d, ", dSource: ", prevDSource, " -> ", dSource);
            
            // 重新确定初始下单挡位
            DetermineInitialLevel();
            
            // 如果价格区间明显变化，清除交易区域记录
            if(MathAbs(startPrice - lastStartPrice) > Point * 20 || MathAbs(endPrice - lastEndPrice) > Point * 20)
            {
               ClearTradedPriceZones();
            }
         }
         else if(d > 0 && initialLevel < 0)
         {
            // 如果有有效的d值但初始下单挡位无效，重新确定初始下单挡位
            if(!TestMode)
               Print("有效d值但初始下单挡位无效，重新确定初始下单挡位");
            DetermineInitialLevel();
         }
      }
   }
   else if(isNewBar || needUpdate)  // 持仓时只更新时间，但不更新ZigZag转折点
   {
      if(isNewBar)
         lastBarTime = currentBarTime;
      
      lastAnalysisTime = currentTime;
      
      // 在持仓时也需要显示交易信息和当前状态
      if(!TestMode || IsVisualMode())
      {
         // 清除并重新创建交易信息标签
         ObjectsDeleteAll(0, "TradeInfo");
         string tradeInfoName = "TradeInfo";
         if(ObjectCreate(0, tradeInfoName, OBJ_LABEL, 0, 0, 0))
         {
            // 构建交易信息文本
            string tradeInfo = "交易设置:\n";
            tradeInfo += "初始档位: " + (initialLevel >= 0 ? IntegerToString(initialLevel + 1) : "无") + "\n";
            tradeInfo += "已下单档位:";
            
            for(int i = 0; i < 8; i++)
            {
               if(levelOrders[i])
                  tradeInfo += " " + IntegerToString(i + 1);
            }
            
            tradeInfo += "\n";
            int displayMaxOrders = EnableMaxOrders ? MaxOrdersCount : SLLevelOffset;
            tradeInfo += "订单数量: " + IntegerToString(ordersPlaced) + "/" + IntegerToString(displayMaxOrders) + "\n";
            tradeInfo += "持仓中：不更新ZigZag转折点\n";
            
            if(takeProfitPrice > 0)
            {
               tradeInfo += "止盈价格: " + DoubleToString(takeProfitPrice, Digits);
               if(EnableFixedTP && isFixedTPSet)
                  tradeInfo += " (固定)";
               tradeInfo += "\n";
            }

            if(stopLossPrice > 0)
               tradeInfo += "止损价格: " + DoubleToString(stopLossPrice, Digits) + "\n";

            if(EnableFixedTP)
               tradeInfo += "固定止盈: " + (isFixedTPSet ? "已设置(第" + IntegerToString(FixedTPOrderNumber) + "单)" : "未设置") + "\n";

            if(EnableMaxOrders)
               tradeInfo += "最多单数: " + IntegerToString(MaxOrdersCount) + " (独立限制)\n";
            else
               tradeInfo += "最多单数: " + IntegerToString(SLLevelOffset) + " (跟随止损档位)\n";

            if(hasClosedPosition)
               tradeInfo += "状态: 已平仓，等待新d值\n";

            // 新增功能状态
            if(EnableInitialLevelEntry)
               tradeInfo += "触及初始挡位: " + (hasInitialLevelOrder ? "已下单" : "等待条件") + "\n";

            if(EnableTrailingStop)
               tradeInfo += "移动止损: 已启用\n";

            // 设置标签属性
            ObjectSetString(0, tradeInfoName, OBJPROP_TEXT, tradeInfo);
            ObjectSetInteger(0, tradeInfoName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
            ObjectSetInteger(0, tradeInfoName, OBJPROP_XDISTANCE, 10);
            ObjectSetInteger(0, tradeInfoName, OBJPROP_YDISTANCE, 15);
            ObjectSetInteger(0, tradeInfoName, OBJPROP_FONTSIZE, FontSize);
            ObjectSetInteger(0, tradeInfoName, OBJPROP_COLOR, clrYellow);
         }
         
         ChartRedraw(0);
      }
   }
   
   // 处理交易逻辑（仅当启用交易且d值有效）- 每个报价都检查
   if(EnableTrading && d > 0)
   {
      // 检查是否需要进行交易
      ManageTrading();

      // 管理现有持仓的止盈止损
      ManageOpenPositions();
   }

   // 处理触及初始挡位下单功能
   if(EnableInitialLevelEntry && d > 0)
   {
      ManageInitialLevelEntry();
   }

   // 处理移动止损功能
   if(EnableTrailingStop && hasOpenPosition)
   {
      ManageTrailingStop();
   }
}

//+------------------------------------------------------------------+
//| 查找最近的三个ZigZag转折点                                        |
//+------------------------------------------------------------------+
void FindZigZagTurningPoints()
{
   int foundPoints = 0;
   int shift = StartBar - 1; // 从指定的K线开始查找（减1是因为数组索引从0开始）
   
   // 清除旧数据
   for(int i = 0; i < 3; i++)
   {
      lastTurningPoints[i] = 0;
      lastTurningPrices[i] = 0;
   }
   
   while(foundPoints < 3 && shift < 1000) // 限制搜索范围，避免无限循环
   {
      double zigzagValue = iCustom(NULL, 0, "ZigZag", ZigZag_Depth, ZigZag_Deviation, ZigZag_Backstep, 0, shift);
      
      // 如果ZigZag值不为0，则找到了一个转折点
      if(zigzagValue != 0 && zigzagValue != EMPTY_VALUE)
      {
         lastTurningPoints[foundPoints] = Time[shift];
         lastTurningPrices[foundPoints] = zigzagValue;
         foundPoints++;
      }
      
      shift++;
   }
   
   // 如果没有找到足够的转折点，输出日志
   if(foundPoints < 3 && !TestMode)
   {
      Print("警告: 只找到 ", foundPoints, " 个ZigZag转折点");
   }
}

//+------------------------------------------------------------------+
//| 分析趋势                                                          |
//+------------------------------------------------------------------+
void AnalyzeTrend()
{
   // 确保有足够的转折点进行分析
   if(lastTurningPoints[0] == 0 || lastTurningPoints[1] == 0 || lastTurningPoints[2] == 0)
   {
      trendDirection = "无法判断趋势 - 转折点不足";
      dSource = 0;
      return;
   }
   
   // 计算差值的绝对值
   d1 = MathAbs(lastTurningPrices[0] - lastTurningPrices[1]);
   d2 = MathAbs(lastTurningPrices[1] - lastTurningPrices[2]);
   
   // 找出最大差值
   d = MathMax(d1, d2);
   
   // 判断趋势
   if(d == d1) // 最大差值来源于z1和z2之间
   {
      dSource = 1;
      startPrice = lastTurningPrices[1]; // 起点为z2
      endPrice = lastTurningPrices[0];   // 终点为z1
      
      if(lastTurningPrices[0] > lastTurningPrices[1])
      {
         trendDirection = "看涨趋势";
      }
      else
      {
         trendDirection = "看跌趋势";
      }
   }
   else // 最大差值来源于z2和z3之间
   {
      dSource = 2;
      startPrice = lastTurningPrices[2]; // 起点为z3
      endPrice = lastTurningPrices[1];   // 终点为z2
      
      if(lastTurningPrices[1] > lastTurningPrices[2])
      {
         trendDirection = "看涨趋势";
      }
      else
      {
         trendDirection = "看跌趋势";
      }
   }
}

//+------------------------------------------------------------------+
//| 绘制斐波那契线                                                    |
//+------------------------------------------------------------------+
void DrawFibonacciLines()
{
   // 回测模式非可视化时跳过绘图
   if(TestMode && !IsVisualMode()) return;
   
   if(dSource == 0) return; // 如果没有确定d值来源，则不绘制
   
   // 获取最新K线的时间
   datetime currentTime = Time[0];
   
   // 计算延伸线的结束时间（向右延伸一定距离）
   datetime endTime = currentTime + (currentTime - Time[10]) * 3;
   
   // 确定回测线的起点和终点
   datetime startTime, endTimePoint;
   double startLevel, endLevel;
   
   // 根据d值来源确定起点时间和价格水平
   if(dSource == 1) // d来源于d1 (z1-z2)
   {
      startTime = lastTurningPoints[0]; // 使用z1的时间作为起点
      endTimePoint = lastTurningPoints[1]; // z2的时间作为终点
      startLevel = lastTurningPrices[0]; // z1价格
      endLevel = lastTurningPrices[1];   // z2价格
   }
   else // d来源于d2 (z2-z3)
   {
      startTime = lastTurningPoints[1]; // 使用z2的时间作为起点
      endTimePoint = lastTurningPoints[2]; // z3的时间作为终点
      startLevel = lastTurningPrices[1]; // z2价格
      endLevel = lastTurningPrices[2];   // z3价格
   }
   
   // 计算价格范围
   double range = MathAbs(endLevel - startLevel);
   
   // 确定趋势方向
   bool isUptrend = (endLevel > startLevel);
   
   // 获取图表的价格范围
   double minPrice = ChartGetDouble(0, CHART_PRICE_MIN);
   double maxPrice = ChartGetDouble(0, CHART_PRICE_MAX);
   int chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
   int chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
   
   // 计算标签的位置 - 将标签放在更靠近图表中间的位置
   // 计算中间位置：起点时间和当前时间的中间点
   datetime labelTime = startTime + (currentTime - startTime) / 2;
   
   // 绘制斐波那契线
   for(int i = 0; i < ArraySize(fiboLevels); i++)
   {
      // 计算当前斐波那契水平的价格
      double fiboPrice;
      
      if(isUptrend) // 上升趋势
      {
         fiboPrice = startLevel + range * fiboLevels[i];
      }
      else // 下降趋势
      {
         fiboPrice = startLevel - range * fiboLevels[i];
      }
      
      // 创建水平线
      string lineName = "FiboLine_" + DoubleToString(fiboLevels[i], 3);
      
      if(ObjectCreate(0, lineName, OBJ_TREND, 0, startTime, fiboPrice, endTime, fiboPrice))
      {
         ObjectSetInteger(0, lineName, OBJPROP_COLOR, FiboLineColor);
         ObjectSetInteger(0, lineName, OBJPROP_STYLE, FiboLineStyle);
         ObjectSetInteger(0, lineName, OBJPROP_WIDTH, FiboLineWidth);
         ObjectSetInteger(0, lineName, OBJPROP_BACK, true);
         ObjectSetInteger(0, lineName, OBJPROP_SELECTABLE, false);
         ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, true); // 向右延伸
      }
      
      // 使用OBJ_TEXT直接在线上添加标签，放在图表中间位置
      string labelName = "FiboLabel_" + DoubleToString(fiboLevels[i], 3);
      if(ObjectCreate(0, labelName, OBJ_TEXT, 0, labelTime, fiboPrice))
      {
         // 构建标签文本 - 显示档位编号、斐波那契水平和价格
         string levelText = "第" + IntegerToString(i+1) + "档: " + DoubleToString(fiboLevels[i], 3) + 
                           " (" + DoubleToString(fiboPrice, Digits) + ")";
         
         // 设置标签属性
         ObjectSetString(0, labelName, OBJPROP_TEXT, levelText);
         ObjectSetInteger(0, labelName, OBJPROP_COLOR, FiboLineColor);
         ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 9);
         ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT);
         ObjectSetInteger(0, labelName, OBJPROP_ZORDER, 2000); // 确保显示在最前面
      }
      
      // 在起点处添加额外的简洁标签
      string startLabelName = "FiboStartLabel_" + DoubleToString(fiboLevels[i], 3);
      if(ObjectCreate(0, startLabelName, OBJ_TEXT, 0, startTime, fiboPrice))
      {
         // 显示档位编号和斐波那契水平
         string levelText = IntegerToString(i+1) + ":" + DoubleToString(fiboLevels[i], 3);
         
         // 设置标签属性
         ObjectSetString(0, startLabelName, OBJPROP_TEXT, levelText);
         ObjectSetInteger(0, startLabelName, OBJPROP_COLOR, FiboLineColor);
         ObjectSetInteger(0, startLabelName, OBJPROP_FONTSIZE, 8);
         ObjectSetInteger(0, startLabelName, OBJPROP_ANCHOR, ANCHOR_RIGHT);
         ObjectSetInteger(0, startLabelName, OBJPROP_ZORDER, 2000); // 确保显示在最前面
      }
   }
   
   // 标记初始下单区域
   if(initialLevel >= 0 && initialLevel < ArraySize(levelOrders))
   {
      // 计算初始下单档位对应的价格
      double fiboLevel = fiboLevels[initialLevel];
      double fiboPrice;
      
      if(isUptrend)
      {
         fiboPrice = startLevel + range * fiboLevel;
      }
      else
      {
         fiboPrice = startLevel - range * fiboLevel;
      }
      
      // 创建区域标记
      string rectName = "InitialOrderLevel";
      datetime rectEndTime = startTime + (currentTime - startTime) / 2;
      
      if(ObjectCreate(0, rectName, OBJ_ARROW, 0, startTime, fiboPrice))
      {
         ObjectSetInteger(0, rectName, OBJPROP_ARROWCODE, 159); // 圆形标记
         ObjectSetInteger(0, rectName, OBJPROP_COLOR, clrYellow);
         ObjectSetInteger(0, rectName, OBJPROP_WIDTH, 5);
         ObjectSetInteger(0, rectName, OBJPROP_SELECTABLE, false);
      }
      
      // 添加描述标签
      string initLabelName = "InitialLevelLabel";
      if(ObjectCreate(0, initLabelName, OBJ_TEXT, 0, startTime, fiboPrice))
      {
         string labelText = "初始下单位置: 第" + IntegerToString(initialLevel+1) + 
                           "档 (" + DoubleToString(fiboLevels[initialLevel], 3) + ")";
         ObjectSetString(0, initLabelName, OBJPROP_TEXT, labelText);
         ObjectSetInteger(0, initLabelName, OBJPROP_COLOR, clrYellow);
         ObjectSetInteger(0, initLabelName, OBJPROP_FONTSIZE, 9);
         ObjectSetInteger(0, initLabelName, OBJPROP_ANCHOR, ANCHOR_RIGHT);
         ObjectSetInteger(0, initLabelName, OBJPROP_ZORDER, 3000);
      }
      

   }
   else
   {
      Print("初始下单档位无效: ", initialLevel);
   }
   
   // 添加一个额外的描述标签，说明这是斐波那契回测线
   string infoLabelName = "FiboInfo";
   if(ObjectCreate(0, infoLabelName, OBJ_LABEL, 0, 0, 0))
   {
      string infoText = "斐波那契回测线 (" + (isUptrend ? "上升" : "下降") + "趋势)";
      if(dSource == 1)
         infoText += " [z1→z2]";
      else
         infoText += " [z2→z3]";
         
      ObjectSetString(0, infoLabelName, OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, infoLabelName, OBJPROP_COLOR, FiboLineColor);
      ObjectSetInteger(0, infoLabelName, OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, infoLabelName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, infoLabelName, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, infoLabelName, OBJPROP_YDISTANCE, 5);
   }
   
   // 绘制起点到终点的连接线，清晰显示波动方向
   string waveLineName = "FiboWaveLine";
   if(ObjectCreate(0, waveLineName, OBJ_TREND, 0, startTime, startPrice, endTimePoint, endPrice))
   {
      color waveColor = isUptrend ? BullishColor : BearishColor;
      ObjectSetInteger(0, waveLineName, OBJPROP_COLOR, waveColor);
      ObjectSetInteger(0, waveLineName, OBJPROP_WIDTH, 2);
      ObjectSetInteger(0, waveLineName, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetInteger(0, waveLineName, OBJPROP_RAY_RIGHT, false);
      ObjectSetInteger(0, waveLineName, OBJPROP_BACK, false);
   }
}

//+------------------------------------------------------------------+
//| 在图表上标记转折点                                                |
//+------------------------------------------------------------------+
void MarkTurningPoints()
{
   // 回测模式非可视化时跳过绘图
   if(TestMode && !IsVisualMode()) return;

   // 先执行原有的标记功能
   // 清除之前的标记
   ObjectsDeleteAll(0, "ZigZagPoint_");
   ObjectsDeleteAll(0, "ZigZagLabel_");
   ObjectsDeleteAll(0, "ZigZagLine_");
   ObjectsDeleteAll(0, "TrendInfo");
   ObjectsDeleteAll(0, "TrendInfo2"); // 添加新标签的删除
   ObjectsDeleteAll(0, "FiboLine_");
   ObjectsDeleteAll(0, "FiboLabel_");
   ObjectsDeleteAll(0, "FiboPriceLabel_");
   ObjectsDeleteAll(0, "FiboStartLabel_");
   ObjectsDeleteAll(0, "FiboInfo");
   ObjectsDeleteAll(0, "FiboWaveLine");
   ObjectsDeleteAll(0, "TradeInfo");
   ObjectsDeleteAll(0, "LevelLabel_");
   ObjectsDeleteAll(0, "InitialOrderLevel");
   ObjectsDeleteAll(0, "InitialLevelLabel");
   
   // 标记找到的转折点
   for(int i = 0; i < 3; i++)
   {
      if(lastTurningPoints[i] == 0) continue; // 跳过未找到的点
      
      string objName = "ZigZagPoint_" + IntegerToString(i);
      string pointName = "z" + IntegerToString(i+1); // 命名为z1, z2, z3
      
      // 创建箭头对象
      if(ObjectCreate(0, objName, OBJ_ARROW, 0, lastTurningPoints[i], lastTurningPrices[i]))
      {
         // 设置箭头的属性
         ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, 159); // 圆形标记
         ObjectSetInteger(0, objName, OBJPROP_COLOR, PointColor);
         ObjectSetInteger(0, objName, OBJPROP_WIDTH, PointSize);
         ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
         ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1000); // 确保显示在最前面
      }
      
      // 添加价格标签和点名称
      string labelName = "ZigZagLabel_" + IntegerToString(i);
      if(ObjectCreate(0, labelName, OBJ_TEXT, 0, lastTurningPoints[i], lastTurningPrices[i]))
      {
         string priceText = pointName + ": " + DoubleToString(lastTurningPrices[i], Digits);
         ObjectSetString(0, labelName, OBJPROP_TEXT, priceText);
         ObjectSetInteger(0, labelName, OBJPROP_COLOR, PointColor);
         ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
         ObjectSetInteger(0, labelName, OBJPROP_SELECTABLE, false);
         // 将标签向上偏移一些，避免与点重叠
         ObjectSetDouble(0, labelName, OBJPROP_ANGLE, 0);
         ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
      }
   }
   
   // 连接转折点
   if(lastTurningPoints[0] != 0 && lastTurningPoints[1] != 0)
   {
      string lineName = "ZigZagLine_0_1";
      ObjectCreate(0, lineName, OBJ_TREND, 0, lastTurningPoints[0], lastTurningPrices[0], 
                                             lastTurningPoints[1], lastTurningPrices[1]);
      ObjectSetInteger(0, lineName, OBJPROP_COLOR, PointColor);
      ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, false);
   }
   
   if(lastTurningPoints[1] != 0 && lastTurningPoints[2] != 0)
   {
      string lineName = "ZigZagLine_1_2";
      ObjectCreate(0, lineName, OBJ_TREND, 0, lastTurningPoints[1], lastTurningPrices[1], 
                                             lastTurningPoints[2], lastTurningPrices[2]);
      ObjectSetInteger(0, lineName, OBJPROP_COLOR, PointColor);
      ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, false);
   }
   
   // 绘制斐波那契线
   if(ShowFiboLines && dSource > 0)
   {

      DrawFibonacciLines();
   }
   
   // 在图表左上角显示趋势信息
   string infoName = "TrendInfo";
   if(ObjectCreate(0, infoName, OBJ_LABEL, 0, 0, 0))
   {
      // 构建显示文本 - 第一部分：趋势和价格
      string infoText = "趋势: " + trendDirection + "\n";
      infoText += "z1:" + DoubleToString(lastTurningPrices[0], Digits) + " ";
      infoText += "z2:" + DoubleToString(lastTurningPrices[1], Digits) + " ";
      infoText += "z3:" + DoubleToString(lastTurningPrices[2], Digits);
      
      // 设置标签属性
      ObjectSetString(0, infoName, OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, infoName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, infoName, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, infoName, OBJPROP_YDISTANCE, 15);
      ObjectSetInteger(0, infoName, OBJPROP_FONTSIZE, FontSize);
      
      // 根据趋势设置颜色
      if(trendDirection == "看涨趋势")
         ObjectSetInteger(0, infoName, OBJPROP_COLOR, BullishColor);
      else if(trendDirection == "看跌趋势")
         ObjectSetInteger(0, infoName, OBJPROP_COLOR, BearishColor);
      else
         ObjectSetInteger(0, infoName, OBJPROP_COLOR, clrWhite);
   }
   
   // 创建第二个标签，显示d值和初始下单挡位信息
   string infoName2 = "TrendInfo2";
   if(ObjectCreate(0, infoName2, OBJ_LABEL, 0, 0, 0))
   {
      // 构建显示文本 - 第二部分：d值和初始下单挡位
      string infoText = "d1=" + DoubleToString(d1, 1) + " d2=" + DoubleToString(d2, 1);
      infoText += " d=" + DoubleToString(d, 1) + "(d" + IntegerToString(dSource) + ")\n";
      infoText += "初始挡位: " + (initialLevel >= 0 ? "第" + IntegerToString(initialLevel+1) + "档" : "无");
      
      // 设置标签属性
      ObjectSetString(0, infoName2, OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, infoName2, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, infoName2, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, infoName2, OBJPROP_YDISTANCE, 15 + FontSize * 3); // 放在第一个标签下方
      ObjectSetInteger(0, infoName2, OBJPROP_FONTSIZE, FontSize);
      
      // 使用与第一个标签相同的颜色
      if(trendDirection == "看涨趋势")
         ObjectSetInteger(0, infoName2, OBJPROP_COLOR, BullishColor);
      else if(trendDirection == "看跌趋势")
         ObjectSetInteger(0, infoName2, OBJPROP_COLOR, BearishColor);
      else
         ObjectSetInteger(0, infoName2, OBJPROP_COLOR, clrWhite);
   }
   
   // 显示交易信息
   if(EnableTrading && d > 0)
   {
      string tradeInfoName = "TradeInfo";
      if(ObjectCreate(0, tradeInfoName, OBJ_LABEL, 0, 0, 0))
      {
         // 构建交易信息文本
         string tradeInfo = "交易设置:\n";
         tradeInfo += "初始档位: " + (initialLevel >= 0 ? IntegerToString(initialLevel + 1) : "无") + "\n";
         tradeInfo += "已下单档位:";
         
         for(int i = 0; i < 8; i++)
         {
            if(levelOrders[i])
               tradeInfo += " " + IntegerToString(i + 1);
         }
         
         tradeInfo += "\n";
         int displayMaxOrders = EnableMaxOrders ? MaxOrdersCount : SLLevelOffset;
         tradeInfo += "订单数量: " + IntegerToString(ordersPlaced) + "/" + IntegerToString(displayMaxOrders) + "\n";
         
         if(takeProfitPrice > 0)
         {
            tradeInfo += "止盈价格: " + DoubleToString(takeProfitPrice, Digits);
            if(EnableFixedTP && isFixedTPSet)
               tradeInfo += " (固定)";
            tradeInfo += "\n";
         }

         if(stopLossPrice > 0)
            tradeInfo += "止损价格: " + DoubleToString(stopLossPrice, Digits) + "\n";

         if(EnableFixedTP)
            tradeInfo += "固定止盈: " + (isFixedTPSet ? "已设置(第" + IntegerToString(FixedTPOrderNumber) + "单)" : "未设置") + "\n";

         if(EnableMaxOrders)
            tradeInfo += "最多单数: " + IntegerToString(MaxOrdersCount) + " (独立限制)\n";
         else
            tradeInfo += "最多单数: " + IntegerToString(SLLevelOffset) + " (跟随止损档位)\n";

         if(hasClosedPosition)
            tradeInfo += "状态: 已平仓，等待新d值\n";

         // 新增功能状态
         if(EnableInitialLevelEntry)
            tradeInfo += "触及初始挡位: " + (hasInitialLevelOrder ? "已下单" : "等待条件") + "\n";

         if(EnableTrailingStop)
            tradeInfo += "移动止损: 已启用";

         // 设置标签属性
         ObjectSetString(0, tradeInfoName, OBJPROP_TEXT, tradeInfo);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_XDISTANCE, 10);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_YDISTANCE, 15);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_FONTSIZE, FontSize);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_COLOR, clrYellow);
      }
   }
   
   // 刷新图表
   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 重置交易状态                                                      |
//+------------------------------------------------------------------+
void ResetTradeStatus()
{
   // 重置所有交易状态变量
   for(int i = 0; i < ArraySize(levelOrders); i++)
   {
      levelOrders[i] = false;
   }
   
   ordersPlaced = 0;
   maxOrderLevel = 0;
   takeProfitPrice = 0;  // 重置止盈价格
   stopLossPrice = 0;    // 重置止损价格
   lastDChange = TimeCurrent();
   // 注意：不重置initialLevel，由DetermineInitialLevel函数设置
   hasClosedPosition = false;
   positionDirection = "";
   openPositionD = 0;
   isFirstPositionPlaced = false;
   fixedTakeProfitPrice = 0;    // [AI修改] 重置固定止盈价格
   isFixedTPSet = false;        // [AI修改] 重置固定止盈标志

   // 重置新增功能变量
   hasInitialLevelOrder = false;
   initialLevelOrderTicket = 0;
   lastTrailingStopCheck = 0;

   // 重置价格检查的静态变量（需要在ManageTrading函数外部声明）
   ResetLastCheckedPrice();
   
   // 检查是否有与当前EA相关的持仓单
   hasOpenPosition = false;
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            hasOpenPosition = true;
            // 记录持仓方向
            if(OrderType() == OP_BUY)
               positionDirection = "BUY";
            else if(OrderType() == OP_SELL)
               positionDirection = "SELL";
            
            // 记录开仓时的d值
            if(d > 0)
               openPositionD = d;
               
            isFirstPositionPlaced = true;
            
            // 记录现有订单的止盈止损
            if(OrderTakeProfit() != 0)
               takeProfitPrice = OrderTakeProfit();
               
            if(OrderStopLoss() != 0)
               stopLossPrice = OrderStopLoss();
               
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 重置价格检查静态变量                                              |
//+------------------------------------------------------------------+
void ResetLastCheckedPrice()
{
   // 这个函数将在ManageTrading外部重置lastCheckedPrice静态变量
   // 在MQL4中我们无法直接从外部重置其他函数的静态变量
   // 因此我们使用全局变量来标记需要重置
   GlobalVariableSet("ZigZag_ResetLastCheckedPrice", 1);
}

//+------------------------------------------------------------------+
//| 确定初始下单挡位                                                  |
//+------------------------------------------------------------------+
void DetermineInitialLevel()
{
   // 根据d值确定初始下单挡位
   // 使用输入参数设置的阈值和档位
   
   double pointsMultiplier = 1;
   if(Digits == 3 || Digits == 5) // JPY对或微型账户
      pointsMultiplier = 10;
   
   // 直接使用d值，不进行复杂的点数转换
   // 这样可以避免因为不同货币对的Digits值不同导致的问题
   double dPoints = d;
   

   
   int oldInitialLevel = initialLevel; // 保存旧的初始下单挡位
   
   if(dPoints > D_Value_High)
   {
      initialLevel = Initial_Level_High; // 默认为第三档

   }
   else if(dPoints > D_Value_Mid)
   {
      initialLevel = Initial_Level_Mid; // 默认为第四档

   }
   else if(dPoints > D_Value_Low)
   {
      initialLevel = Initial_Level_Low; // 默认为第五档

   }
   // 新增条件 - 处理10<d<20的情况
   else if(dPoints > D_Value_VeryLow)
   {
      initialLevel = Initial_Level_VeryLow; // 设置为第六档

   }
   else if(dPoints > D_Value_SuperLow)
   {
      initialLevel = Initial_Level_SuperLow; // 设置为第七档

   }
   else
   {
      initialLevel = -1; // d值太小，不进行交易

   }
   

   
   // 如果初始下单挡位发生变化，重新绘制图表
   if(initialLevel != oldInitialLevel)
   {
      if(!TestMode)
         Print("初始下单挡位变化: ", (oldInitialLevel >= 0 ? IntegerToString(oldInitialLevel+1) : "无"), 
               " -> ", (initialLevel >= 0 ? IntegerToString(initialLevel+1) : "无"));
      
      // 清除旧的标记
      ObjectsDeleteAll(0, "InitialOrderLevel");
      ObjectsDeleteAll(0, "InitialLevelLabel");
      
      // 重新绘制斐波那契线和初始下单区域
      if(ShowFiboLines && dSource > 0 && (!TestMode || IsVisualMode()))
      {
         MarkTurningPoints(); // 重新绘制所有标记
      }
   }
}

//+------------------------------------------------------------------+
//| 获取档位描述                                                      |
//+------------------------------------------------------------------+
string GetLevelDescription(int level)
{
   if(level < 0 || level >= ArraySize(fiboLevels) - 1)
      return "无效档位";
      
   return DoubleToString(fiboLevels[level], 3) + "-" + DoubleToString(fiboLevels[level+1], 3);
}

//+------------------------------------------------------------------+
//| 计算指定档位的价格范围                                            |
//+------------------------------------------------------------------+
bool GetLevelPriceRange(int level, double &startLevelPrice, double &endLevelPrice)
{
   if(level < 0 || level >= ArraySize(fiboLevels) - 1 || dSource == 0)
   {
      if(!TestMode)
         Print("GetLevelPriceRange: 无效参数 - level=", level, ", dSource=", dSource);
      return false;
   }
      
   bool isUptrend = (trendDirection == "看涨趋势");
   double range = MathAbs(endPrice - startPrice);
   
   // 确保不超出数组范围
   int lowerIndex = MathMin(level, ArraySize(fiboLevels) - 2);
   int upperIndex = MathMin(level + 1, ArraySize(fiboLevels) - 1);
   

   
   if(isUptrend)
   {
      // 上升趋势，计算斐波那契回调价格
      // 斐波那契回调线的方向：从低点到高点，回调百分比从高点向下计算
      startLevelPrice = endPrice - range * fiboLevels[upperIndex]; // 档位下边界
      endLevelPrice = endPrice - range * fiboLevels[lowerIndex];   // 档位上边界
      

   }
   else
   {
      // 下降趋势，计算斐波那契回调价格
      // 斐波那契回调线的方向：从高点到低点，回调百分比从低点向上计算
      startLevelPrice = endPrice + range * fiboLevels[lowerIndex];   // 档位下边界
      endLevelPrice = endPrice + range * fiboLevels[upperIndex]; // 档位上边界
      

   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查价格是否在已交易的区域内                                     |
//+------------------------------------------------------------------+
bool IsPriceInTradedZone(double price)
{
   for(int i = 0; i < tradedPriceZonesCount; i++)
   {
      if(price >= tradedPriceZones[i][0] && price <= tradedPriceZones[i][1])
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 添加一个已交易的价格区间                                         |
//+------------------------------------------------------------------+
void AddTradedPriceZone(double zoneStartPrice, double zoneEndPrice)
{
   // 确保记录不超过最大限制
   if(tradedPriceZonesCount >= MAX_PRICE_MEMORY)
   {
      // 移除最旧的记录
      for(int i = 0; i < MAX_PRICE_MEMORY - 1; i++)
      {
         tradedPriceZones[i][0] = tradedPriceZones[i+1][0];
         tradedPriceZones[i][1] = tradedPriceZones[i+1][1];
      }
      tradedPriceZonesCount = MAX_PRICE_MEMORY - 1;
   }
   
   // 添加新记录
   tradedPriceZones[tradedPriceZonesCount][0] = zoneStartPrice;
   tradedPriceZones[tradedPriceZonesCount][1] = zoneEndPrice;
   tradedPriceZonesCount++;
   
   if(!TestMode)
      Print("添加已交易区域: ", zoneStartPrice, " - ", zoneEndPrice);
}

//+------------------------------------------------------------------+
//| 清除所有已交易区域记录                                           |
//+------------------------------------------------------------------+
void ClearTradedPriceZones()
{
   tradedPriceZonesCount = 0;
   if(!TestMode)
      Print("已清除所有交易区域记录");
}

//+------------------------------------------------------------------+
//| 管理交易                                                          |
//+------------------------------------------------------------------+
void ManageTrading()
{
   // 如果没有有效的初始挡位或已平仓，不进行交易
   if(initialLevel < 0 || hasClosedPosition)
      return;
      
   // [AI修改] 检查下单数量限制
   int maxAllowedOrders;
   if(EnableMaxOrders)
   {
      // 使用独立的最多单数限制
      maxAllowedOrders = MaxOrdersCount;
   }
   else
   {
      // 使用止损档位偏移作为最多单数
      maxAllowedOrders = SLLevelOffset;
   }

   if(ordersPlaced >= maxAllowedOrders)
   {
      if(!TestMode)
         Print("已达到最大下单数量限制: ", ordersPlaced, "/", maxAllowedOrders);
      return;
   }
      
   // 确定当前的趋势方向和价格
   string currentTrendDirection = trendDirection;
   
   // 如果已经有了第一个持仓，锁定交易方向
   if(isFirstPositionPlaced && positionDirection != "")
   {
      // 检查趋势方向是否与持仓方向匹配
      bool directionMatched = (positionDirection == "BUY" && currentTrendDirection == "看涨趋势") ||
                              (positionDirection == "SELL" && currentTrendDirection == "看跌趋势");
      
      if(!directionMatched)
      {
         if(!TestMode)
            Print("当前趋势方向(", currentTrendDirection, ")与持仓方向(", positionDirection, ")不匹配，不开新仓");
         return;
      }
   }
   
   // 获取当前价格
   double currentPrice = (currentTrendDirection == "看涨趋势") ? Ask : Bid;
   
   // 使用静态变量跟踪上一次检查的价格，以判断是否首次穿越斐波那契点位
   static double lastCheckedPrice = 0;
   
   // 检查是否需要重置lastCheckedPrice
   if(GlobalVariableCheck("ZigZag_ResetLastCheckedPrice"))
   {
      if(GlobalVariableGet("ZigZag_ResetLastCheckedPrice") == 1)
      {
         lastCheckedPrice = 0;
         GlobalVariableDel("ZigZag_ResetLastCheckedPrice");
         if(!TestMode)
            Print("已重置价格检查状态");
      }
   }
   
   // 回测模式下预计算一些共用值，避免重复计算
   double range = MathAbs(endPrice - startPrice);
   bool isUptrend = (trendDirection == "看涨趋势");
   double priceTolerance = Point * 10; // 允许10点的误差范围
   
   // [AI修改] 检查斐波那契水平点位，最多检查指定数量的档位
   for(int level = initialLevel; level < initialLevel + maxAllowedOrders && level < ArraySize(fiboLevels) - 1; level++)
   {
      // 如果该档位已经下单，跳过
      if(levelOrders[level])
         continue;
      
      // 获取该档位对应的斐波那契水平
      double fiboLevel = fiboLevels[level];
      double fiboPrice;
      
      // 根据趋势方向计算斐波那契水平对应的价格
      if(isUptrend)
      {
         // 上升趋势，斐波那契回调价格 = 终点价格 - 范围 * 斐波那契水平
         fiboPrice = endPrice - range * fiboLevel;
      }
      else
      {
         // 下降趋势，斐波那契回调价格 = 终点价格 + 范围 * 斐波那契水平
         fiboPrice = endPrice + range * fiboLevel;
      }

      // 启用提前入场时，根据趋势方向调整入场价格：在价格到达目标回调位/反弹位之前入场（更容易达到）
      if(EnableAdvancedEntry)
      {
         if(isUptrend)
         {
            // 上涨趋势：在回调还没到位时入场（价格更高的位置）
            fiboPrice = fiboPrice + range * AdvancedEntryDistance;
         }
         else
         {
            // 下跌趋势：在反弹还没到位时入场（价格更低的位置）
            fiboPrice = fiboPrice - range * AdvancedEntryDistance;
         }
      }
      
      // 检查价格是否接近或穿越斐波那契点位
      bool crossedFiboLevel = false;
      
      if(isUptrend)
      {
         // 看涨趋势，价格上升穿越斐波那契水平
         crossedFiboLevel = (currentPrice >= fiboPrice-priceTolerance && currentPrice <= fiboPrice+priceTolerance) &&
                           (lastCheckedPrice == 0 || lastCheckedPrice < fiboPrice-priceTolerance);
      }
      else
      {
         // 看跌趋势，价格下降穿越斐波那契水平
         crossedFiboLevel = (currentPrice >= fiboPrice-priceTolerance && currentPrice <= fiboPrice+priceTolerance) &&
                           (lastCheckedPrice == 0 || lastCheckedPrice > fiboPrice+priceTolerance);
      }
      
      // 检查该价格点位是否已交易过
      if(IsPriceInTradedZone(fiboPrice))
      {
         continue; // 已交易过该点位，跳过
      }
      
      // 如果价格穿越斐波那契点位，执行下单
      if(crossedFiboLevel)
      {
         // 计算下单手数
         double lotSize = CalculateLotSize(level);
         
         // 执行下单前检查是否已有相反方向的持仓
         if(isFirstPositionPlaced)
         {
            // 已有持仓，检查方向是否一致
            bool canPlaceOrder = (currentTrendDirection == "看涨趋势" && positionDirection == "BUY") ||
                                 (currentTrendDirection == "看跌趋势" && positionDirection == "SELL");
                                 
            if(!canPlaceOrder)
            {
               if(!TestMode)
                  Print("已有相反方向的持仓，不能开相反方向的单");
               continue; // 跳过当前档位
            }
         }
         
         // [AI修改] 在下单前先更新最高档位，确保PlaceOrder函数能使用正确的maxOrderLevel
         maxOrderLevel = MathMax(maxOrderLevel, level);

         // 执行下单（直接带上止盈止损）
         bool orderResult = PlaceOrder(lotSize, level);

         if(orderResult)
         {
            levelOrders[level] = true;  // 标记该档位已下单
            ordersPlaced++;
            
            // 记录交易方向
            if(!isFirstPositionPlaced)
            {
               isFirstPositionPlaced = true;
               positionDirection = (currentTrendDirection == "看涨趋势") ? "BUY" : "SELL";
               // 确保记录开仓时的d值
               if(openPositionD == 0 && d > 0)
               {
                  openPositionD = d;
                  if(!TestMode)
                     Print("在ManageTrading中记录首次交易d值: ", openPositionD);
               }
            }
            
            // [AI修改] 检查是否需要记录固定止盈价格
            if(EnableFixedTP && !isFixedTPSet && ordersPlaced == FixedTPOrderNumber)
            {
               fixedTakeProfitPrice = takeProfitPrice;
               isFixedTPSet = true;
               if(!TestMode)
                  Print("记录第", FixedTPOrderNumber, "单的止盈价格作为固定止盈: ", fixedTakeProfitPrice);
            }

            // 更新止盈止损
            UpdateStopLossAndTakeProfit();

            // 记录该价格点位已交易
            double zoneTolerance = Point * 10;
            AddTradedPriceZone(fiboPrice - zoneTolerance, fiboPrice + zoneTolerance);
            
            // 输出日志
            if(!TestMode)
               Print("在斐波那契点位 ", fiboLevel, " (", fiboPrice, ") 下单成功，当前价格: ", currentPrice, 
                     "，档位: 第", level+1, "档",
                     "，止盈: ", takeProfitPrice, "，止损: ", stopLossPrice);
            
            // 每个tick只处理一个档位的下单，一旦下单成功，就退出循环
            break;
         }
      }
   }
   
   // 更新lastCheckedPrice用于下次比较
   lastCheckedPrice = currentPrice;
}

//+------------------------------------------------------------------+
//| 下单函数，直接设置止盈止损                                         |
//+------------------------------------------------------------------+
bool PlaceOrder(double lotSize, int level)
{
   // 如果是首次下单，记录开仓时的d值
   if(!isFirstPositionPlaced && d > 0)
   {
      openPositionD = d;
      if(!TestMode)
         Print("记录首次开仓d值: ", openPositionD);
   }
   
   // 计算止盈止损价格
   double localTakeProfit = 0;
   double localStopLoss = stopLossPrice;
   
   // 临时保存当前d值
   double currentD = d;
   double currentStartPrice = startPrice;
   double currentEndPrice = endPrice;
   int currentDSource = dSource;
   
   // 如果已有开仓d值，使用它来计算止盈
   if(isFirstPositionPlaced && openPositionD > 0)
   {
      d = openPositionD;
   }
   
   // [AI修改] 声明变量以避免作用域问题
   int takeProfitLevel = 0;
   double priceRange = MathAbs(endPrice - startPrice);
   double fiboTP = 0;

   // [AI修改] 检查是否使用固定止盈价格
   if(EnableFixedTP && isFixedTPSet && ordersPlaced >= FixedTPOrderNumber)
   {
      // 使用固定止盈价格
      localTakeProfit = fixedTakeProfitPrice;
      if(!TestMode)
         Print("使用固定止盈价格: ", localTakeProfit, " (来自第", FixedTPOrderNumber, "单)");
   }
   else
   {
      // [AI修改] 计算止盈价格 - 统一使用最高持仓档位-3的逻辑
      // 更新最高持仓档位
      maxOrderLevel = MathMax(maxOrderLevel, level);
      takeProfitLevel = MathMax(0, maxOrderLevel - TPLevelOffset); // 使用最高持仓档位偏移

      // 直接使用斐波那契水平计算止盈价格
      fiboTP = fiboLevels[takeProfitLevel];

      if(trendDirection == "看涨趋势")
      {
         localTakeProfit = endPrice - priceRange * fiboTP;
      }
      else
      {
         localTakeProfit = endPrice + priceRange * fiboTP;
      }

      // 验证计算出的止盈价格是否有效
      if(!IsValidPrice(localTakeProfit))
      {
         if(!TestMode)
            Print("计算的止盈价格无效: ", localTakeProfit, " 取消下单");
         return false; // 价格无效时不下单
      }

      // [AI修改] 启用提前止盈时，根据趋势方向调整止盈价格：让止盈更容易达到
      if(EnableAdvancedTP)
      {
         if(trendDirection == "看涨趋势")
         {
            // 看涨趋势：降低止盈价格，更容易达到
            localTakeProfit = localTakeProfit - priceRange * AdvancedTPDistance;
         }
         else
         {
            // 看跌趋势：提高止盈价格，更容易达到
            localTakeProfit = localTakeProfit + priceRange * AdvancedTPDistance;
         }
      }


   }
   
   // 恢复当前d值
   d = currentD;
   startPrice = currentStartPrice;
   endPrice = currentEndPrice;
   dSource = currentDSource;
   
   // 计算止损价格
   if(localStopLoss == 0)
   {
      // 判断是否是超低d值范围(5<d<15)
      double dPoints = d;
      bool isSuperlowD = (dPoints > D_Value_SuperLow && dPoints <= D_Value_VeryLow);
      
      if(isSuperlowD)
      {
         // 对于超低d值，使用第十档(1.5)作为止损位置
         if(!TestMode)
            Print("超低d值范围，使用第十档(1.5)作为止损位置");
         // 使用最后一个斐波那契水平(第10档)
         double fiboSL = fiboLevels[10]; // 1.5
         double slPriceRange = MathAbs(endPrice - startPrice);

         // 计算对应的价格
         if(trendDirection == "看涨趋势")
            localStopLoss = startPrice + slPriceRange * fiboSL;
         else
            localStopLoss = startPrice - slPriceRange * fiboSL;

         // 验证计算出的止损价格是否有效
         if(!IsValidPrice(localStopLoss))
         {
            if(!TestMode)
               Print("计算的止损价格无效: ", localStopLoss, " 取消下单");
            return false; // 价格无效时不下单
         }

      }
      else
      {
         // 计算止损档位
         int stopLossLevel = initialLevel + SLLevelOffset;
         
         // 确保不超出最大可用档位
         if(stopLossLevel >= ArraySize(fiboLevels) - 1)
         {
            stopLossLevel = ArraySize(fiboLevels) - 2;
         }
         
         // 直接使用斐波那契水平计算止损价格
         double fiboSL = fiboLevels[stopLossLevel]; // 直接使用计算出的档位，不再+1
         double slPriceRange = MathAbs(endPrice - startPrice);
         
         if(trendDirection == "看涨趋势")
            localStopLoss = endPrice - slPriceRange * fiboSL;
         else
            localStopLoss = endPrice + slPriceRange * fiboSL;

         // 验证计算出的止损价格是否有效
         if(!IsValidPrice(localStopLoss))
         {
            if(!TestMode)
               Print("计算的止损价格无效: ", localStopLoss, " 取消下单");
            return false; // 价格无效时不下单
         }

      }
   }
   
   // 设置全局止盈止损价格
   takeProfitPrice = localTakeProfit;
   stopLossPrice = localStopLoss;
   
   int orderType;
   double price;
   string comment;
   
   // 确保订单方向与当前持仓方向一致
   if(isFirstPositionPlaced && positionDirection != "")
   {
      if(positionDirection == "BUY")
      {
         orderType = OP_BUY;
         price = Ask;
         comment = "ZigZag看涨 档位:" + IntegerToString(level+1) + "(" + DoubleToString(fiboLevels[level], 3) + ")";
      }
      else if(positionDirection == "SELL")
      {
         orderType = OP_SELL;
         price = Bid;
         comment = "ZigZag看跌 档位:" + IntegerToString(level+1) + "(" + DoubleToString(fiboLevels[level], 3) + ")";
      }
      else
      {
         if(!TestMode)
            Print("无效的持仓方向，无法下单");
         return false;
      }
   }
   else // 首次下单
   {
      if(trendDirection == "看涨趋势")
      {
         orderType = OP_BUY;
         price = Ask;
         comment = "ZigZag看涨 档位:" + IntegerToString(level+1) + "(" + DoubleToString(fiboLevels[level], 3) + ")";
      }
      else
      {
         orderType = OP_SELL;
         price = Bid;
         comment = "ZigZag看跌 档位:" + IntegerToString(level+1) + "(" + DoubleToString(fiboLevels[level], 3) + ")";
      }
   }
   
   // 验证并修正止损止盈价格
   double validatedStopLoss = ValidateStopLoss(orderType, price, stopLossPrice);
   double validatedTakeProfit = ValidateTakeProfit(orderType, price, takeProfitPrice);

   // 添加额外的安全检查（0值表示不设置止损/止盈，是有效的）
   if((validatedStopLoss != 0 && !IsValidPrice(validatedStopLoss)) ||
      (validatedTakeProfit != 0 && !IsValidPrice(validatedTakeProfit)))
   {
      if(!TestMode)
         Print("价格验证失败 - 止损: ", validatedStopLoss, " 止盈: ", validatedTakeProfit, " 开仓价: ", price);
      return false;
   }



   // 在下单时直接设置止盈止损
   int ticket = OrderSend(Symbol(), orderType, lotSize, price, Slippage, validatedStopLoss, validatedTakeProfit, comment, MagicNumber);

   if(ticket > 0)
   {
      if(!TestMode)
         Print("订单执行成功: ", comment, " 手数: ", lotSize, " 价格: ", price,
               " 止损: ", validatedStopLoss, " 止盈: ", validatedTakeProfit);
      hasOpenPosition = true;

      // 更新全局止损止盈价格为实际使用的价格
      if(validatedStopLoss != stopLossPrice)
      {
         stopLossPrice = validatedStopLoss;
         if(!TestMode)
            Print("止损价格已调整为: ", stopLossPrice);
      }
      if(validatedTakeProfit != takeProfitPrice)
      {
         takeProfitPrice = validatedTakeProfit;
         if(!TestMode)
            Print("止盈价格已调整为: ", takeProfitPrice);
      }

      return true;
   }
   else
   {
      int error = GetLastError();
      if(!TestMode)
         Print("订单执行失败: 错误代码", error, " 手数: ", lotSize, " 价格: ", price,
               " 原始止损: ", stopLossPrice, " 验证后止损: ", validatedStopLoss,
               " 原始止盈: ", takeProfitPrice, " 验证后止盈: ", validatedTakeProfit);
      return false;
   }
}

//+------------------------------------------------------------------+
//| 计算下单手数                                                      |
//+------------------------------------------------------------------+
double CalculateLotSize(int level)
{
   double lotSize = InitialLotSize;
   
   // 根据档位计算手数倍数
   int levelOffset = level - initialLevel;
   for(int i = 0; i < levelOffset; i++)
   {
      lotSize *= LotMultiplier;
   }
   
   // 资金管理
   if(UseMoneyManagement)
   {
      double riskMoney = AccountBalance() * RiskPercent / 100.0;
      double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
      double stopLevelPoints = 0;
      
      // 获取当前价格
      double currentPrice = (trendDirection == "看涨趋势") ? Ask : Bid;
      
      // 获取该挡位可能的止损点数
      double startLevelPrice, endLevelPrice;
      
      // 计算止损档位，但不限制最大为7
      int stopLossLevel = initialLevel + SLLevelOffset;
      
      // 确保不超出最大可用档位
      if(stopLossLevel >= ArraySize(fiboLevels) - 1)
      {
         stopLossLevel = ArraySize(fiboLevels) - 2;
      }
      
      if(GetLevelPriceRange(stopLossLevel, startLevelPrice, endLevelPrice)) // 初始挡位+3档作为止损
      {
         if(trendDirection == "看涨趋势")
            stopLevelPoints = MathAbs(currentPrice - endLevelPrice) / Point;
         else
            stopLevelPoints = MathAbs(currentPrice - endLevelPrice) / Point;
            

      }
      else
      {
         stopLevelPoints = 100; // 假设100点止损
         Print("警告: 无法获取止损档位价格范围，使用默认100点止损");
      }
      
      if(stopLevelPoints > 0 && tickValue > 0)
      {
         double calculatedLot = NormalizeDouble(riskMoney / (stopLevelPoints * tickValue), 2);
         
         // 应用档位倍数
         for(int i = 0; i < levelOffset; i++)
         {
            calculatedLot *= LotMultiplier;
         }
         
         lotSize = calculatedLot;
      }
   }
   
   // 验证手数范围
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);
   
   lotSize = MathMax(minLot, lotSize);
   lotSize = MathMin(maxLot, lotSize);
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
   
   return lotSize;
}

//+------------------------------------------------------------------+
//| 更新止损和止盈                                                    |
//+------------------------------------------------------------------+
void UpdateStopLossAndTakeProfit()
{
   // 临时保存当前d值
   double oldD = d;
   double oldStartPrice = startPrice;
   double oldEndPrice = endPrice;
   int oldDSource = dSource;
   
   // 如果有记录开仓时的d值，临时替换当前d值用于计算
   if(openPositionD > 0)
   {

      d = openPositionD;
   }
   
   // [AI修改] 声明变量以避免作用域问题
   int takeProfitLevel = 0;
   double fiboTP = 0;

   // [AI修改] 检查是否使用固定止盈价格
   if(EnableFixedTP && isFixedTPSet)
   {
      // 使用固定止盈价格，不再重新计算
      takeProfitPrice = fixedTakeProfitPrice;
      if(!TestMode)
         Print("UpdateStopLossAndTakeProfit: 使用固定止盈价格: ", takeProfitPrice);
   }
   else
   {
      // 计算止盈价格
      // 止盈为持仓最大档位-TPLevelOffset的档位对应的斐波那契水平
      takeProfitLevel = MathMax(0, maxOrderLevel - TPLevelOffset);
      double tpPriceRange = MathAbs(endPrice - startPrice);
      fiboTP = fiboLevels[takeProfitLevel];

      if(trendDirection == "看涨趋势")
      {
         takeProfitPrice = endPrice - tpPriceRange * fiboTP;
      }
      else
      {
         takeProfitPrice = endPrice + tpPriceRange * fiboTP;
      }

      // [AI修改] 启用提前止盈时，根据趋势方向调整止盈价格：让止盈更容易达到
      if(EnableAdvancedTP)
      {
         if(trendDirection == "看涨趋势")
         {
            // 看涨趋势：降低止盈价格，更容易达到
            takeProfitPrice = takeProfitPrice - tpPriceRange * AdvancedTPDistance;
         }
         else
         {
            // 看跌趋势：提高止盈价格，更容易达到
            takeProfitPrice = takeProfitPrice + tpPriceRange * AdvancedTPDistance;
         }
      }


   }
   
   // 恢复原始d值
   if(openPositionD > 0)
   {
      d = oldD;
      startPrice = oldStartPrice;
      endPrice = oldEndPrice;
      dSource = oldDSource;
   }
   
   // 计算止损价格
   // 止损为初始挡位+3档对应的斐波那契水平，一旦设置就不再变更
   if(stopLossPrice == 0)  // 只有在尚未设置止损时才计算新的止损
   {
      // 判断是否是超低d值范围(5<d<15)
      double dPoints = openPositionD > 0 ? openPositionD : d;
      bool isSuperlowD = (dPoints > D_Value_SuperLow && dPoints <= D_Value_VeryLow);
      
      if(isSuperlowD)
      {
         // 对于超低d值，使用第十档(1.5)作为止损位置

         // 使用最后一个斐波那契水平(第10档)
         double fiboSL = fiboLevels[10]; // 1.5
         double slPriceRange = MathAbs(endPrice - startPrice);
         
         if(trendDirection == "看涨趋势")
            stopLossPrice = startPrice + slPriceRange * fiboSL;
         else
            stopLossPrice = startPrice - slPriceRange * fiboSL;
            

      }
      else
      {
         // 计算止损档位
         int stopLossLevel = initialLevel + SLLevelOffset;
         
         // 确保不超出最大可用档位
         if(stopLossLevel >= ArraySize(fiboLevels) - 1)
         {
            stopLossLevel = ArraySize(fiboLevels) - 2;
         }
         
         // 直接使用斐波那契水平计算止损价格
         double fiboSL = fiboLevels[stopLossLevel]; // 直接使用计算出的档位，不再+1
         double slPriceRange = MathAbs(endPrice - startPrice);
         
         if(trendDirection == "看涨趋势")
            stopLossPrice = endPrice - slPriceRange * fiboSL;
         else
            stopLossPrice = endPrice + slPriceRange * fiboSL;
            

      }
   }
   
   // 更新所有订单的止盈止损
   UpdateOrdersStopLossAndTakeProfit();
   

}

//+------------------------------------------------------------------+
//| 管理现有持仓的止盈止损                                            |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
   // 检查是否有开仓
   bool foundOpenPosition = false;
   int highestOrderLevel = -1;
   string detectedPositionDirection = "";
   bool foundInitialLevelOrder = false;

   // 遍历所有订单，找出最大档位
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            foundOpenPosition = true;
            
            // 确定持仓方向
            if(OrderType() == OP_BUY)
               detectedPositionDirection = "BUY";
            else if(OrderType() == OP_SELL)
               detectedPositionDirection = "SELL";
            
            // 从订单注释中提取档位信息
            string comment = OrderComment();
            int pos = StringFind(comment, "档位:");
            if(pos != -1)
            {
               string levelStr = StringSubstr(comment, pos + 6);
               int orderLevel = (int)StringToInteger(levelStr) - 1; // 减1是因为档位从1开始，而level从0开始
               highestOrderLevel = MathMax(highestOrderLevel, orderLevel);
            }
            
            // 记录开仓时的d值（如果尚未记录）
            if(openPositionD == 0 && d > 0)
            {
               openPositionD = d;
               if(!TestMode)
                  Print("记录开仓时的d值: ", openPositionD);
            }
            
            // 记录已设置的止损价格（如果尚未记录）
            if(stopLossPrice == 0 && OrderStopLoss() != 0)
            {
               stopLossPrice = OrderStopLoss();
               if(!TestMode)
                  Print("从订单中记录止损价格: ", stopLossPrice);
            }
         }
         else if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber + 1000)
         {
            // 触及初始挡位订单
            foundOpenPosition = true;
            foundInitialLevelOrder = true;

            // 确定持仓方向
            if(OrderType() == OP_BUY)
               detectedPositionDirection = "BUY";
            else if(OrderType() == OP_SELL)
               detectedPositionDirection = "SELL";

            // 检查订单是否还存在
            if(OrderTicket() == initialLevelOrderTicket)
            {
               hasInitialLevelOrder = true;
            }
         }
      }
   }

   // 更新触及初始挡位订单状态
   if(!foundInitialLevelOrder && hasInitialLevelOrder)
   {
      hasInitialLevelOrder = false;
      initialLevelOrderTicket = 0;
      if(!TestMode)
         Print("触及初始挡位订单已平仓");
   }
   
   // 如果找到持仓且档位有变化，更新maxOrderLevel
   if(foundOpenPosition && highestOrderLevel >= 0 && highestOrderLevel != maxOrderLevel)
   {

      maxOrderLevel = highestOrderLevel;
      
      // 根据最新的最高档位和开仓时的d值重新计算止盈价格
      UpdateStopLossAndTakeProfit();
   }
   
   // 更新持仓方向状态
   if(foundOpenPosition && detectedPositionDirection != "")
   {
      if(positionDirection == "" || positionDirection != detectedPositionDirection)
      {
         positionDirection = detectedPositionDirection;
         if(!TestMode)
            Print("更新持仓方向: ", positionDirection);
         isFirstPositionPlaced = true;
      }
   }
   
   // 如果之前有开仓但现在没有，说明已经平仓
   if(hasOpenPosition && !foundOpenPosition)
   {
      if(!TestMode)
         Print("检测到订单已平仓");
      hasOpenPosition = false;
      hasClosedPosition = true;
      
      // 平仓后重置持仓方向和初始下单标记
      positionDirection = "";
      isFirstPositionPlaced = false;
   }
   
   // 更新持仓状态
   hasOpenPosition = foundOpenPosition;
   
   // 如果有持仓，确保订单的止盈止损与全局变量一致
   if(foundOpenPosition && (takeProfitPrice != 0 || stopLossPrice != 0))
   {
      // 确保所有订单使用相同的止盈止损
      EnsureOrdersHaveSameTPSL();
   }
}

//+------------------------------------------------------------------+
//| 更新所有订单的止盈止损                                            |
//+------------------------------------------------------------------+
void UpdateOrdersStopLossAndTakeProfit()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            // 检查止盈止损是否已经设置
            bool needUpdate = false;

            // 使用更精确的价格比较，避免浮点数精度问题
            double priceDiff = Point * 2; // 允许2个点的误差

            if(takeProfitPrice > 0 && MathAbs(OrderTakeProfit() - takeProfitPrice) > priceDiff)
               needUpdate = true;

            if(stopLossPrice > 0 && (OrderStopLoss() == 0 || MathAbs(OrderStopLoss() - stopLossPrice) > priceDiff))
               needUpdate = true;

            if(needUpdate)
            {
               bool result = OrderModify(OrderTicket(), OrderOpenPrice(), stopLossPrice, takeProfitPrice, 0);
               if(!result)
               {
                  int error = GetLastError();
                  // 只在非测试模式下且错误不是"无变化"时输出错误信息
                  if(!TestMode && error != 1 && error != 0)
                  {
                     Print("修改订单失败: 错误代码", error, " 订单: ", OrderTicket(),
                           " 当前止盈:", OrderTakeProfit(), " 目标止盈:", takeProfitPrice,
                           " 当前止损:", OrderStopLoss(), " 目标止损:", stopLossPrice);
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 确保所有订单使用相同的止盈止损                                    |
//+------------------------------------------------------------------+
void EnsureOrdersHaveSameTPSL()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            // 检查是否真的需要修改，避免不必要的修改操作
            double priceDiff = Point * 2; // 允许2个点的误差
            bool needModify = false;

            if(takeProfitPrice > 0 && MathAbs(OrderTakeProfit() - takeProfitPrice) > priceDiff)
               needModify = true;

            if(stopLossPrice > 0 && (OrderStopLoss() == 0 || MathAbs(OrderStopLoss() - stopLossPrice) > priceDiff))
               needModify = true;

            if(needModify)
            {
               bool result = OrderModify(OrderTicket(), OrderOpenPrice(), stopLossPrice, takeProfitPrice, 0);
               if(!result)
               {
                  int error = GetLastError();
                  // 只在非测试模式下且错误不是"无变化"时输出错误信息
                  if(!TestMode && error != 1 && error != 0)
                  {
                     Print("确保订单止盈止损一致时修改失败: 错误代码", error, " 订单: ", OrderTicket());
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 验证止损价格                                                      |
//+------------------------------------------------------------------+
double ValidateStopLoss(int orderType, double openPrice, double stopLoss)
{
   if(stopLoss <= 0) return 0; // 如果没有设置止损，返回0

   // 检查输入价格的有效性
   if(!IsValidPrice(openPrice) || !IsValidPrice(stopLoss))
   {
      if(!TestMode)
         Print("ValidateStopLoss: 无效价格 - 开仓价: ", openPrice, " 止损价: ", stopLoss);
      return 0;
   }

   // 获取最小止损距离
   double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
   if(minStopLevel == 0) minStopLevel = 10 * Point; // 默认最小10点

   // 对于某些品种，可能需要更大的最小距离
   if(minStopLevel < 5 * Point) minStopLevel = 5 * Point;

   // 规范化价格到正确的小数位数
   double normalizedStopLoss = NormalizeDouble(stopLoss, Digits);
   double normalizedOpenPrice = NormalizeDouble(openPrice, Digits);

   if(orderType == OP_BUY)
   {
      // 买单：止损必须低于开仓价格
      if(normalizedStopLoss >= normalizedOpenPrice)
      {
         normalizedStopLoss = normalizedOpenPrice - minStopLevel;
         if(!TestMode)
            Print("买单止损价格过高，调整为: ", normalizedStopLoss);
      }
      else if(normalizedOpenPrice - normalizedStopLoss < minStopLevel)
      {
         normalizedStopLoss = normalizedOpenPrice - minStopLevel;
         if(!TestMode)
            Print("买单止损距离过近，调整为: ", normalizedStopLoss);
      }
   }
   else if(orderType == OP_SELL)
   {
      // 卖单：止损必须高于开仓价格
      if(normalizedStopLoss <= normalizedOpenPrice)
      {
         normalizedStopLoss = normalizedOpenPrice + minStopLevel;
         if(!TestMode)
            Print("卖单止损价格过低，调整为: ", normalizedStopLoss);
      }
      else if(normalizedStopLoss - normalizedOpenPrice < minStopLevel)
      {
         normalizedStopLoss = normalizedOpenPrice + minStopLevel;
         if(!TestMode)
            Print("卖单止损距离过近，调整为: ", normalizedStopLoss);
      }
   }

   return normalizedStopLoss;
}

//+------------------------------------------------------------------+
//| 验证止盈价格                                                      |
//+------------------------------------------------------------------+
double ValidateTakeProfit(int orderType, double openPrice, double takeProfit)
{
   if(takeProfit <= 0) return 0; // 如果没有设置止盈，返回0

   // 检查输入价格的有效性
   if(!IsValidPrice(openPrice) || !IsValidPrice(takeProfit))
   {
      if(!TestMode)
         Print("ValidateTakeProfit: 无效价格 - 开仓价: ", openPrice, " 止盈价: ", takeProfit);
      return 0;
   }

   // 获取最小止损距离（止盈也使用相同的最小距离）
   double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
   if(minStopLevel == 0) minStopLevel = 10 * Point; // 默认最小10点

   // 对于某些品种，可能需要更大的最小距离
   if(minStopLevel < 5 * Point) minStopLevel = 5 * Point;

   // 规范化价格到正确的小数位数
   double normalizedTakeProfit = NormalizeDouble(takeProfit, Digits);
   double normalizedOpenPrice = NormalizeDouble(openPrice, Digits);

   if(orderType == OP_BUY)
   {
      // 买单：止盈必须高于开仓价格
      if(normalizedTakeProfit <= normalizedOpenPrice)
      {
         normalizedTakeProfit = normalizedOpenPrice + minStopLevel;
         if(!TestMode)
            Print("买单止盈价格过低，调整为: ", normalizedTakeProfit);
      }
      else if(normalizedTakeProfit - normalizedOpenPrice < minStopLevel)
      {
         normalizedTakeProfit = normalizedOpenPrice + minStopLevel;
         if(!TestMode)
            Print("买单止盈距离过近，调整为: ", normalizedTakeProfit);
      }
   }
   else if(orderType == OP_SELL)
   {
      // 卖单：止盈必须低于开仓价格
      if(normalizedTakeProfit >= normalizedOpenPrice)
      {
         normalizedTakeProfit = normalizedOpenPrice - minStopLevel;
         if(!TestMode)
            Print("卖单止盈价格过高，调整为: ", normalizedTakeProfit);
      }
      else if(normalizedOpenPrice - normalizedTakeProfit < minStopLevel)
      {
         normalizedTakeProfit = normalizedOpenPrice - minStopLevel;
         if(!TestMode)
            Print("卖单止盈距离过近，调整为: ", normalizedTakeProfit);
      }
   }

   return normalizedTakeProfit;
}

//+------------------------------------------------------------------+
//| 验证价格是否有效                                                  |
//+------------------------------------------------------------------+
bool IsValidPrice(double price)
{
   // 检查价格是否为有效数值
   if(price <= 0 || price != price) // price != price 检查NaN
      return false;

   // 检查价格是否在合理范围内
   double currentPrice = (Ask + Bid) / 2;
   double maxDeviation = currentPrice * 10; // 允许当前价格的10倍偏差

   if(price > currentPrice + maxDeviation || price < currentPrice - maxDeviation)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| 管理触及初始挡位下单功能                                          |
//+------------------------------------------------------------------+
void ManageInitialLevelEntry()
{
   // 如果已经有触及初始挡位订单，不再下单
   if(hasInitialLevelOrder)
      return;

   // 检查初始挡位是否有效
   if(initialLevel < 0)
      return;

   // 确保有足够的K线数据
   if(Bars < 4)
      return;

   // 获取初始挡位价格
   double initialLevelPrice = GetInitialLevelPrice();
   if(initialLevelPrice <= 0)
      return;

   // 检查K线形态条件
   if(CheckInitialLevelEntryCondition(initialLevelPrice))
   {
      // 执行下单
      PlaceInitialLevelOrder();
   }
}

//+------------------------------------------------------------------+
//| 获取初始挡位价格                                                  |
//+------------------------------------------------------------------+
double GetInitialLevelPrice()
{
   if(initialLevel < 0 || initialLevel >= ArraySize(fiboLevels) || dSource == 0)
      return 0;

   double fiboLevel = fiboLevels[initialLevel];
   double range = MathAbs(endPrice - startPrice);
   double initialPrice;

   if(trendDirection == "看涨趋势")
   {
      initialPrice = endPrice - range * fiboLevel;
   }
   else
   {
      initialPrice = endPrice + range * fiboLevel;
   }

   // 如果启用提前入场，调整价格
   if(EnableAdvancedEntry)
   {
      if(trendDirection == "看涨趋势")
      {
         initialPrice = initialPrice + range * AdvancedEntryDistance;
      }
      else
      {
         initialPrice = initialPrice - range * AdvancedEntryDistance;
      }
   }

   return initialPrice;
}

//+------------------------------------------------------------------+
//| 检查触及初始挡位入场条件                                          |
//+------------------------------------------------------------------+
bool CheckInitialLevelEntryCondition(double initialLevelPrice)
{
   // 检查趋势方向
   if(trendDirection == "看涨趋势")
   {
      // 上涨趋势条件检查
      // 上上上柱或上上柱最低点低于初始挡位价格
      bool lowTouchCondition = (Low[3] < initialLevelPrice) || (Low[2] < initialLevelPrice);

      // 上上柱和上柱都是阳线
      bool bullishBars = (Close[2] > Open[2]) && (Close[1] > Open[1]);

      // 上上上柱为阴线
      bool bearishBar3 = (Close[3] < Open[3]);

      return lowTouchCondition && bullishBars && bearishBar3;
   }
   else if(trendDirection == "看跌趋势")
   {
      // 下降趋势条件检查
      // 上上上柱或上上柱最高点高于初始挡位价格
      bool highTouchCondition = (High[3] > initialLevelPrice) || (High[2] > initialLevelPrice);

      // 上上柱和上柱都是阴线
      bool bearishBars = (Close[2] < Open[2]) && (Close[1] < Open[1]);

      // 上上上柱为阳线
      bool bullishBar3 = (Close[3] > Open[3]);

      return highTouchCondition && bearishBars && bullishBar3;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 下触及初始挡位订单                                                |
//+------------------------------------------------------------------+
void PlaceInitialLevelOrder()
{
   double lotSize = InitialLotSize;
   int orderType;
   double price;
   double stopLoss;
   string comment;

   // 计算前三柱的最高点和最低点
   double highestHigh = MathMax(MathMax(High[1], High[2]), High[3]);
   double lowestLow = MathMin(MathMin(Low[1], Low[2]), Low[3]);

   if(trendDirection == "看涨趋势")
   {
      orderType = OP_BUY;
      price = Ask;
      stopLoss = lowestLow - 50 * Point;
      comment = "ZigZag初始挡位买单";
   }
   else
   {
      orderType = OP_SELL;
      price = Bid;
      stopLoss = highestHigh + 50 * Point;
      comment = "ZigZag初始挡位卖单";
   }

   // 验证止损价格
   double validatedStopLoss = ValidateStopLoss(orderType, price, stopLoss);

   // 下单（不设置止盈，只设置止损）
   int ticket = OrderSend(Symbol(), orderType, lotSize, price, Slippage, validatedStopLoss, 0, comment, MagicNumber + 1000);

   if(ticket > 0)
   {
      hasInitialLevelOrder = true;
      initialLevelOrderTicket = ticket;
      hasOpenPosition = true;

      if(!TestMode)
         Print("触及初始挡位订单执行成功: ", comment, " 手数: ", lotSize, " 价格: ", price, " 止损: ", validatedStopLoss);
   }
   else
   {
      int error = GetLastError();
      if(!TestMode)
         Print("触及初始挡位订单执行失败: 错误代码", error);
   }
}

//| 管理移动止损功能                                                  |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
   // 只在新K线出现时检查
   datetime currentBarTime = Time[0];
   if(currentBarTime == lastTrailingStopCheck)
      return;

   lastTrailingStopCheck = currentBarTime;

   // 确保有足够的K线数据
   if(Bars < 2)
      return;

   // 计算盈利阈值
   double profitThreshold = 0.1 * d * 100 * Point;

   // 遍历所有订单
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && (OrderMagicNumber() == MagicNumber || OrderMagicNumber() == MagicNumber + 1000))
         {
            double currentPrice = (OrderType() == OP_BUY) ? Bid : Ask;
            double profit = 0;
            double newStopLoss = 0;
            bool shouldUpdate = false;

            if(OrderType() == OP_BUY)
            {
               // 多单：检测上一柱最低点是否盈利
               double checkPrice = Low[1];
               profit = (checkPrice - OrderOpenPrice()) / Point;

               if(profit >= profitThreshold / Point)
               {
                  newStopLoss = checkPrice - 44 * Point;

                  // 确保新止损高于当前止损
                  if(OrderStopLoss() == 0 || newStopLoss > OrderStopLoss())
                  {
                     shouldUpdate = true;
                  }
               }
            }
            else if(OrderType() == OP_SELL)
            {
               // 空单：检测上一柱最高点是否盈利
               double checkPrice = High[1];
               profit = (OrderOpenPrice() - checkPrice) / Point;

               if(profit >= profitThreshold / Point)
               {
                  newStopLoss = checkPrice + 44 * Point;

                  // 确保新止损低于当前止损
                  if(OrderStopLoss() == 0 || newStopLoss < OrderStopLoss())
                  {
                     shouldUpdate = true;
                  }
               }
            }

            if(shouldUpdate)
            {
               // 验证止损价格
               double validatedStopLoss = ValidateStopLoss(OrderType(), OrderOpenPrice(), newStopLoss);

               // 修改订单止损
               bool result = OrderModify(OrderTicket(), OrderOpenPrice(), validatedStopLoss, OrderTakeProfit(), 0);

               if(result)
               {
                  if(!TestMode)
                     Print("移动止损成功: 订单", OrderTicket(), " 新止损: ", validatedStopLoss, " 盈利: ", profit, "点");
               }
               else
               {
                  int error = GetLastError();
                  if(!TestMode && error != 1 && error != 0)
                     Print("移动止损失败: 订单", OrderTicket(), " 错误代码: ", error);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+